# Email-Based Authentication Setup

This document explains how to set up and use the new email-based authentication system in Quizzio.

## Overview

The authentication system has been updated to use email and password instead of phone numbers. Users must verify their email address before they can log in.

## Features

- **Email Registration**: Users register with name, email, and password
- **Email Verification**: Users receive a verification email with a link to activate their account
- **Secure Login**: Only verified users can log in
- **Password Protection**: Passwords are hashed using bcrypt with salt rounds of 12

## API Endpoints

### 1. User Registration
```
POST /api/users/register
```

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Response:**
```json
{
  "message": "Registration successful! Please check your email to verify your account.",
  "user": {
    "id": "user_id",
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "emailVerified": false
  }
}
```

### 2. Email Verification
```
GET /api/users/verify-email?token=verification_token
```

**Response:**
```json
{
  "success": true,
  "message": "<PERSON>ail verified successfully! You can now log in."
}
```

### 3. User Login
```
POST /api/users/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "token": "jwt_token_here",
  "user": {
    "id": "user_id",
    "name": "John Doe",
    "email": "<EMAIL>",
    "emailVerified": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "lastLogin": "2024-01-01T00:00:00.000Z",
    "totalQuizzes": 0,
    "totalTests": 0
  }
}
```

## Email Configuration

To enable email functionality, configure these environment variables in your `.env` file:

```env
# Email Configuration
BASE_URL=http://localhost:3000
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>
```

### Gmail Setup

1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a password for "Mail"
3. Use the generated password as `SMTP_PASS`

## Security Features

- **Password Validation**: Minimum 6 characters required
- **Email Validation**: Proper email format validation
- **Token Expiration**: Verification tokens expire after 24 hours
- **Secure Storage**: Passwords are hashed, verification tokens are not exposed in API responses
- **JWT Authentication**: Secure token-based authentication for protected routes

## Migration from Phone-Based Auth

The system has been completely migrated from phone-based to email-based authentication:

- User model now includes `name`, `email`, `emailVerified` fields
- JWT tokens now use `email` and `userId` instead of `phoneNumber`
- All protected routes now authenticate using email-based user lookup
- Database indexes updated for email-based queries

## Error Handling

Common error responses:

- **400**: Invalid input (missing fields, invalid email format, weak password)
- **401**: Authentication failed (invalid credentials, unverified email)
- **409**: User already exists
- **500**: Server error (database issues, email service problems)

## Testing

To test the email functionality:

1. Set up email configuration in `.env`
2. Register a new user
3. Check your email for the verification link
4. Click the verification link
5. Log in with your credentials

Note: In development, email service errors are logged but won't prevent registration from completing.
