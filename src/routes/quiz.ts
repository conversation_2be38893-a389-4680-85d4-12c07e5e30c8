import { Router, Request, Response } from 'express';
import { Types } from 'mongoose';
import { User } from '../models/User.js';
import { Quiz } from '../models/Quiz.js';
import { authenticateToken } from '../middleware/auth.js';
import { generateQuizFromText } from '../utils/quizGenerator.js';
import { IQuizGeneration, IQuizResponse } from '../types/index.js';

const router = Router();

/**
 * POST /api/quiz/generate
 * Generate a quiz from text with difficulty level and question type
 */
router.post('/generate', authenticateToken, async (req: Request<{}, any, IQuizGeneration>, res: Response) => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated',
        message: 'Authentication required'
      });
      return;
    }

    const { text, difficulty, questionType, numberOfQuestions, subject } = req.body;
    const userId = req.user.userId;

    // Validate input
    if (!text || text.trim().length === 0) {
      res.status(400).json({
        success: false,
        error: 'Text is required',
        message: 'Please provide text content to generate quiz from'
      });
      return;
    }

    const validDifficulties = ['easy', 'medium', 'hard'] as const;
    const validQuestionTypes = ['multiple-choice', 'true-false', 'short-answer', 'mixed'] as const;

    if (difficulty && !validDifficulties.includes(difficulty)) {
      res.status(400).json({
        success: false,
        error: 'Invalid difficulty',
        message: `Difficulty must be one of: ${validDifficulties.join(', ')}`
      });
      return;
    }

    if (questionType && !validQuestionTypes.includes(questionType)) {
      res.status(400).json({
        success: false,
        error: 'Invalid question type',
        message: `Question type must be one of: ${validQuestionTypes.join(', ')}`
      });
      return;
    }

    // Generate quiz using AI
    const generatedQuiz = await generateQuizFromText(
      text,
      difficulty || 'medium',
      questionType || 'multiple-choice',
      numberOfQuestions || 5,
      subject
    );

    // Create quiz object
    const quiz = new Quiz({
      userId,
      title: `Quiz from "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`,
      text,
      difficulty: difficulty || 'medium',
      questionType: questionType || 'multiple-choice',
      questions: generatedQuiz.questions,
      metadata: {
        textLength: text.length,
        numberOfQuestions: generatedQuiz.questions.length,
        estimatedTime: generatedQuiz.questions.length * 2 // 2 minutes per question
      }
    });

    // Save quiz to database
    const savedQuiz = await quiz.save();

    // Add quiz to user's quiz list
    await User.findByIdAndUpdate(
      userId,
      { $push: { quizzes: (savedQuiz._id as any).toString() } }
    );

    const quizResponse: IQuizResponse = {
      id: (savedQuiz._id as any).toString(),
      title: savedQuiz.title,
      difficulty: savedQuiz.difficulty,
      questionType: savedQuiz.questionType,
      questions: savedQuiz.questions,
      metadata: savedQuiz.metadata,
      createdAt: savedQuiz.createdAt.toISOString()
    };

    res.status(201).json({
      success: true,
      message: 'Quiz generated successfully',
      data: quizResponse
    });

  } catch (error) {
    console.error('Quiz generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Quiz generation failed',
      message: 'An error occurred while generating the quiz'
    });
  }
});

/**
 * GET /api/quizzes
 * Fetch all quizzes saved by the authenticated user
 */
router.get('/', authenticateToken, async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated',
        message: 'Authentication required'
      });
      return;
    }

    const userId = req.user.userId;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Get user's quizzes with pagination
    const quizzes = await Quiz.find({ userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    const totalQuizzes = await Quiz.countDocuments({ userId });
    const totalPages = Math.ceil(totalQuizzes / limit);

    const quizSummaries = quizzes.map(quiz => ({
      id: quiz._id.toString(),
      title: quiz.title,
      difficulty: quiz.difficulty,
      questionType: quiz.questionType,
      numberOfQuestions: quiz.questions.length,
      createdAt: quiz.createdAt.toISOString(),
      metadata: quiz.metadata
    }));

    res.json({
      success: true,
      message: `Found ${quizSummaries.length} quizzes`,
      data: {
        quizzes: quizSummaries,
        pagination: {
          currentPage: page,
          totalPages,
          totalQuizzes,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Fetch quizzes error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch quizzes',
      message: 'An error occurred while fetching quizzes'
    });
  }
});

/**
 * GET /api/quizzes/:id
 * Get a specific quiz by ID
 */
router.get('/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated',
        message: 'Authentication required'
      });
      return;
    }

    const quizId = req.params.id;
    const userId = req.user.userId;

    // Validate ObjectId
    if (!quizId || !Types.ObjectId.isValid(quizId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid quiz ID',
        message: 'The provided quiz ID is not valid'
      });
      return;
    }

    const quiz = await Quiz.findById(quizId).lean();

    if (!quiz) {
      res.status(404).json({
        success: false,
        error: 'Quiz not found',
        message: 'The requested quiz does not exist'
      });
      return;
    }

    // Check if user owns this quiz
    if (quiz.userId !== userId) {
      res.status(403).json({
        success: false,
        error: 'Access denied',
        message: 'You do not have permission to access this quiz'
      });
      return;
    }

    const quizResponse: IQuizResponse = {
      id: quiz._id.toString(),
      title: quiz.title,
      difficulty: quiz.difficulty,
      questionType: quiz.questionType,
      questions: quiz.questions,
      metadata: quiz.metadata,
      createdAt: quiz.createdAt.toISOString()
    };

    res.json({
      success: true,
      message: 'Quiz retrieved successfully',
      data: quizResponse
    });

  } catch (error) {
    console.error('Get quiz error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get quiz',
      message: 'An error occurred while retrieving the quiz'
    });
  }
});

/**
 * DELETE /api/quizzes/:id
 * Delete a specific quiz by ID
 */
router.delete('/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated',
        message: 'Authentication required'
      });
      return;
    }

    const quizId = req.params.id;
    const userId = req.user.userId;

    // Validate ObjectId
    if (!quizId || !Types.ObjectId.isValid(quizId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid quiz ID',
        message: 'The provided quiz ID is not valid'
      });
      return;
    }

    const quiz = await Quiz.findOneAndDelete({ _id: quizId, userId });

    if (!quiz) {
      res.status(404).json({
        success: false,
        error: 'Quiz not found',
        message: 'The requested quiz does not exist or you do not have permission to delete it'
      });
      return;
    }

    // Remove quiz from user's quiz list
    await User.findByIdAndUpdate(
      userId,
      { $pull: { quizzes: quizId } }
    );

    res.json({
      success: true,
      message: 'Quiz deleted successfully'
    });

  } catch (error) {
    console.error('Delete quiz error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete quiz',
      message: 'An error occurred while deleting the quiz'
    });
  }
});

export default router;
