import { Router, Request, Response } from 'express';
import { authenticateToken } from '../middleware/auth.js';
import GeminiService from '../services/geminiService.js';
import database from '../utils/database.js';

const router = Router();

/**
 * GET /api/admin/health
 * Comprehensive health check including AI services
 */
router.get('/health', authenticateToken, async (req: Request, res: Response) => {
  try {
    const dbStatus = database.getConnectionInfo();
    let geminiStatus: { connected: boolean; error: string | null } = { connected: false, error: null };

    // Test Gemini connection if API key is available
    if (process.env.GEMINI_API_KEY) {
      try {
        const geminiService = new GeminiService();
        const isConnected = await geminiService.testConnection();
        geminiStatus = { connected: isConnected, error: null };
      } catch (error) {
        geminiStatus = {
          connected: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }

    res.json({
      success: true,
      message: 'System health check completed',
      data: {
        server: {
          status: 'running',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          environment: process.env.NODE_ENV || 'development',
          version: '2.0.0'
        },
        database: {
          connected: dbStatus.isConnected,
          readyState: dbStatus.readyState,
          host: dbStatus.host,
          port: dbStatus.port,
          name: dbStatus.name
        },
        ai: {
          gemini: geminiStatus,
          apiKeyConfigured: !!process.env.GEMINI_API_KEY
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      success: false,
      error: 'Health check failed',
      message: 'An error occurred during health check'
    });
  }
});

/**
 * POST /api/admin/test-quiz
 * Test quiz generation with sample content
 */
router.post('/test-quiz', authenticateToken, async (req: Request, res: Response) => {
  try {
    const {
      difficulty = 'medium',
      questionType = 'multiple-choice',
      numberOfQuestions = 3
    } = req.body;

    const sampleText = `
Artificial Intelligence (AI) is a branch of computer science that aims to create intelligent machines
that can perform tasks that typically require human intelligence. These tasks include learning,
reasoning, problem-solving, perception, and language understanding.

Machine Learning is a subset of AI that enables computers to learn and improve from experience
without being explicitly programmed. It uses algorithms to analyze data, identify patterns,
and make predictions or decisions.

Deep Learning is a subset of machine learning that uses neural networks with multiple layers
to process and learn from vast amounts of data. It has been particularly successful in areas
like image recognition, natural language processing, and speech recognition.
    `.trim();

    if (!process.env.GEMINI_API_KEY) {
      res.status(400).json({
        success: false,
        error: 'Gemini API key not configured',
        message: 'Please set GEMINI_API_KEY in your environment variables'
      });
      return;
    }

    const geminiService = new GeminiService();
    const result = await geminiService.generateQuiz({
      text: sampleText,
      difficulty: difficulty as 'easy' | 'medium' | 'hard',
      questionType: questionType as 'multiple-choice' | 'true-false' | 'short-answer' | 'mixed',
      numberOfQuestions,
      subject: 'Artificial Intelligence'
    });

    res.json({
      success: true,
      message: 'Test quiz generated successfully',
      data: {
        quiz: result,
        sampleText: sampleText.substring(0, 200) + '...'
      }
    });

  } catch (error) {
    console.error('Test quiz generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Test quiz generation failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

/**
 * GET /api/admin/stats
 * Get system statistics
 */
router.get('/stats', authenticateToken, async (req: Request, res: Response) => {
  try {
    // This would typically query the database for actual stats
    // For now, returning mock data structure
    res.json({
      success: true,
      message: 'System statistics retrieved',
      data: {
        users: {
          total: 0,
          active: 0,
          newToday: 0
        },
        quizzes: {
          total: 0,
          generatedToday: 0,
          averageQuestions: 0
        },
        tests: {
          total: 0,
          completedToday: 0,
          averageScore: 0
        },
        ai: {
          requestsToday: 0,
          successRate: 0,
          averageResponseTime: 0
        }
      }
    });

  } catch (error) {
    console.error('Stats retrieval error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve statistics',
      message: 'An error occurred while retrieving system statistics'
    });
  }
});

export default router;
