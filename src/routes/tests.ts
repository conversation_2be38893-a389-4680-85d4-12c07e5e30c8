import { Router, Request, Response } from 'express';
import { Types } from 'mongoose';
import { User } from '../models/User.js';
import { Quiz } from '../models/Quiz.js';
import { Test } from '../models/Test.js';
import { authenticateToken } from '../middleware/auth.js';
import { calculateTestScore, generateTestFeedback } from '../utils/quizGenerator.js';
import { ITestSubmission, ITestResponse, ITestStatistics } from '../types/index.js';

const router = Router();

/**
 * POST /api/tests/submit
 * Save the results of a completed test
 */
router.post('/submit', authenticateToken, async (req: Request<{}, any, ITestSubmission>, res: Response) => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated',
        message: 'Authentication required'
      });
      return;
    }

    const { quizId, answers, timeSpent, startTime, endTime } = req.body;
    const userId = req.user.userId;

    // Validate input
    if (!quizId) {
      res.status(400).json({
        success: false,
        error: 'Quiz ID is required',
        message: 'Please provide a valid quiz ID'
      });
      return;
    }

    if (!answers || !Array.isArray(answers)) {
      res.status(400).json({
        success: false,
        error: 'Answers are required',
        message: 'Please provide answers as an array'
      });
      return;
    }

    // Validate ObjectId
    if (!Types.ObjectId.isValid(quizId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid quiz ID',
        message: 'The provided quiz ID is not valid'
      });
      return;
    }

    // Get the quiz to validate answers
    const quiz = await Quiz.findById(quizId);
    if (!quiz) {
      res.status(404).json({
        success: false,
        error: 'Quiz not found',
        message: 'The specified quiz does not exist'
      });
      return;
    }

    // Check if user has access to this quiz
    if (quiz.userId !== userId) {
      res.status(403).json({
        success: false,
        error: 'Access denied',
        message: 'You do not have permission to submit answers for this quiz'
      });
      return;
    }

    // Calculate score
    const scoreData = calculateTestScore(answers, quiz.questions);
    const feedback = generateTestFeedback(scoreData.percentage);

    // Create test result object
    const test = new Test({
      userId,
      quizId,
      answers: answers.map((answer, index) => ({
        questionId: quiz.questions[index]?.id || `q${index + 1}`,
        answer
      })),
      score: scoreData.score,
      totalQuestions: scoreData.totalQuestions,
      correctAnswers: scoreData.correctAnswers,
      percentage: scoreData.percentage,
      timeSpent: timeSpent || undefined,
      startTime: startTime ? new Date(startTime) : undefined,
      endTime: endTime ? new Date(endTime) : new Date(),
      feedback
    });

    // Save test result
    const savedTest = await test.save();

    // Add test to user's test history
    await User.findByIdAndUpdate(
      userId,
      { $push: { tests: (savedTest._id as any).toString() } }
    );

    const testResponse: ITestResponse = {
      id: (savedTest._id as any).toString(),
      quizId: savedTest.quizId,
      score: savedTest.score,
      totalQuestions: savedTest.totalQuestions,
      correctAnswers: savedTest.correctAnswers,
      percentage: savedTest.percentage,
      timeSpent: savedTest.timeSpent,
      feedback: savedTest.feedback,
      submittedAt: savedTest.submittedAt.toISOString()
    };

    res.status(201).json({
      success: true,
      message: 'Test submitted successfully',
      data: testResponse
    });

  } catch (error) {
    console.error('Test submission error:', error);
    res.status(500).json({
      success: false,
      error: 'Test submission failed',
      message: 'An error occurred while submitting the test'
    });
  }
});

/**
 * GET /api/tests
 * Get all test results for the authenticated user
 */
router.get('/', authenticateToken, async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated',
        message: 'Authentication required'
      });
      return;
    }

    const userId = req.user.userId;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Get user's test results with pagination
    const tests = await Test.find({ userId })
      .sort({ submittedAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    const totalTests = await Test.countDocuments({ userId });
    const totalPages = Math.ceil(totalTests / limit);

    const testSummaries = tests.map(test => ({
      id: test._id.toString(),
      quizId: test.quizId,
      score: test.score,
      totalQuestions: test.totalQuestions,
      correctAnswers: test.correctAnswers,
      percentage: test.percentage,
      timeSpent: test.timeSpent,
      submittedAt: test.submittedAt.toISOString()
    }));

    // Calculate user statistics
    const statistics: ITestStatistics = calculateUserStatistics(tests);

    res.json({
      success: true,
      message: `Found ${testSummaries.length} test results`,
      data: {
        tests: testSummaries,
        statistics,
        pagination: {
          currentPage: page,
          totalPages,
          totalTests,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Fetch tests error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch test results',
      message: 'An error occurred while fetching test results'
    });
  }
});

/**
 * GET /api/tests/:id
 * Get a specific test result by ID
 */
router.get('/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated',
        message: 'Authentication required'
      });
      return;
    }

    const testId = req.params.id;
    const userId = req.user.userId;

    // Validate ObjectId
    if (!testId || !Types.ObjectId.isValid(testId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid test ID',
        message: 'The provided test ID is not valid'
      });
      return;
    }

    const test = await Test.findById(testId).lean();

    if (!test) {
      res.status(404).json({
        success: false,
        error: 'Test result not found',
        message: 'The requested test result does not exist'
      });
      return;
    }

    // Check if user owns this test result
    if (test.userId !== userId) {
      res.status(403).json({
        success: false,
        error: 'Access denied',
        message: 'You do not have permission to access this test result'
      });
      return;
    }

    const testResponse = {
      id: test._id.toString(),
      userId: test.userId,
      quizId: test.quizId,
      answers: test.answers,
      score: test.score,
      totalQuestions: test.totalQuestions,
      correctAnswers: test.correctAnswers,
      percentage: test.percentage,
      timeSpent: test.timeSpent,
      startTime: test.startTime?.toISOString(),
      endTime: test.endTime.toISOString(),
      submittedAt: test.submittedAt.toISOString(),
      feedback: test.feedback
    };

    res.json({
      success: true,
      message: 'Test result retrieved successfully',
      data: testResponse
    });

  } catch (error) {
    console.error('Get test error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get test result',
      message: 'An error occurred while retrieving the test result'
    });
  }
});

/**
 * DELETE /api/tests/:id
 * Delete a specific test result by ID
 */
router.delete('/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated',
        message: 'Authentication required'
      });
      return;
    }

    const testId = req.params.id;
    const userId = req.user.userId;

    // Validate ObjectId
    if (!testId || !Types.ObjectId.isValid(testId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid test ID',
        message: 'The provided test ID is not valid'
      });
      return;
    }

    const test = await Test.findOneAndDelete({ _id: testId, userId });

    if (!test) {
      res.status(404).json({
        success: false,
        error: 'Test result not found',
        message: 'The requested test result does not exist or you do not have permission to delete it'
      });
      return;
    }

    // Remove test from user's test list
    await User.findByIdAndUpdate(
      userId,
      { $pull: { tests: testId } }
    );

    res.json({
      success: true,
      message: 'Test result deleted successfully'
    });

  } catch (error) {
    console.error('Delete test error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete test result',
      message: 'An error occurred while deleting the test result'
    });
  }
});

// Helper function to calculate user statistics
function calculateUserStatistics(tests: any[]): ITestStatistics {
  if (tests.length === 0) {
    return {
      averageScore: 0,
      totalTests: 0,
      bestScore: 0,
      averageTime: 0
    };
  }

  const totalScore = tests.reduce((sum, test) => sum + test.percentage, 0);
  const averageScore = Math.round(totalScore / tests.length);
  const bestScore = Math.max(...tests.map(test => test.percentage));

  const testsWithTime = tests.filter(test => test.timeSpent);
  const averageTime = testsWithTime.length > 0
    ? Math.round(testsWithTime.reduce((sum, test) => sum + test.timeSpent, 0) / testsWithTime.length)
    : 0;

  return {
    averageScore,
    totalTests: tests.length,
    bestScore,
    averageTime
  };
}

export default router;
