import { Router, Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import { User } from '../models/User.js';
import { authenticateToken, generateToken } from '../middleware/auth.js';
import { emailService } from '../services/emailService.js';
import { IUserLogin, IUserRegistration, ILoginResponse, IUserResponse, IRegistrationResponse } from '../types/index.js';

const router = Router();

/**
 * POST /api/users/register
 * Register a new user with email verification
 */
router.post('/register', async (req: Request<{}, IRegistrationResponse, IUserRegistration>, res: Response<IRegistrationResponse>) => {
  try {
    const { name, email, password } = req.body;

    // Validate input
    if (!name || !email || !password) {
      res.status(400).json({
        message: 'Name, email, and password are required',
        user: {} as any
      });
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      res.status(400).json({
        message: 'Please provide a valid email address',
        user: {} as any
      });
      return;
    }

    // Validate password strength
    if (password.length < 6) {
      res.status(400).json({
        message: 'Password must be at least 6 characters long',
        user: {} as any
      });
      return;
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      res.status(409).json({
        message: 'An account with this email already exists',
        user: {} as any
      });
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Generate verification token
    const verificationToken = emailService.generateVerificationToken();
    const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Create new user
    const user = new User({
      name: name.trim(),
      email: email.toLowerCase().trim(),
      password: hashedPassword,
      emailVerified: false,
      emailVerificationToken: verificationToken,
      emailVerificationExpires: verificationExpires,
      quizzes: [],
      tests: []
    });

    await user.save();

    // Send verification email
    const emailSent = await emailService.sendVerificationEmail({
      name: user.name,
      email: user.email,
      verificationToken
    });

    if (!emailSent) {
      console.error('Failed to send verification email for user:', user.email);
    }

    console.log(`👤 New user registered: ${user.email}`);

    res.status(201).json({
      message: 'Registration successful! Please check your email to verify your account.',
      user: {
        id: (user._id as any).toString(),
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      message: 'An error occurred during registration',
      user: {} as any
    });
  }
});

/**
 * GET /api/users/verify-email
 * Verify user email address
 */
router.get('/verify-email', async (req: Request, res: Response) => {
  try {
    const { token } = req.query;

    if (!token || typeof token !== 'string') {
      res.status(400).json({
        success: false,
        message: 'Verification token is required'
      });
      return;
    }

    // Find user with this verification token
    const user = await User.findOne({
      emailVerificationToken: token,
      emailVerificationExpires: { $gt: new Date() }
    });

    if (!user) {
      res.status(400).json({
        success: false,
        message: 'Invalid or expired verification token'
      });
      return;
    }

    // Update user as verified
    user.emailVerified = true;
    user.emailVerificationToken = null as any;
    user.emailVerificationExpires = null as any;
    await user.save();

    console.log(`✅ Email verified for user: ${user.email}`);

    // Redirect to a success page or return success response
    res.status(200).json({
      success: true,
      message: 'Email verified successfully! You can now log in.'
    });

  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred during email verification'
    });
  }
});

/**
 * POST /api/users/login
 * Authenticate a user using email and password
 */
router.post('/login', async (req: Request<{}, ILoginResponse, IUserLogin>, res: Response<ILoginResponse>) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      res.status(400).json({
        message: 'Email and password are required',
        token: '',
        user: {} as IUserResponse
      });
      return;
    }

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });

    if (!user) {
      res.status(401).json({
        message: 'Invalid email or password',
        token: '',
        user: {} as IUserResponse
      });
      return;
    }

    // Check if email is verified
    if (!user.emailVerified) {
      res.status(401).json({
        message: 'Please verify your email address before logging in',
        token: '',
        user: {} as IUserResponse
      });
      return;
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      res.status(401).json({
        message: 'Invalid email or password',
        token: '',
        user: {} as IUserResponse
      });
      return;
    }

    // Generate JWT token
    const token = generateToken({
      email: user.email,
      userId: (user._id as any).toString()
    });

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    const userResponse: IUserResponse = {
      id: (user._id as any).toString(),
      name: user.name,
      email: user.email,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt.toISOString(),
      lastLogin: user.lastLogin.toISOString(),
      totalQuizzes: user.quizzes.length,
      totalTests: user.tests.length
    };

    res.status(200).json({
      message: 'Login successful',
      token,
      user: userResponse
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      message: 'An error occurred during login',
      token: '',
      user: {} as IUserResponse
    });
  }
});

/**
 * GET /api/users/profile
 * Get user profile (protected route)
 */
router.get('/profile', authenticateToken, async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated',
        message: 'Authentication required'
      });
      return;
    }

    const user = await User.findOne({ email: req.user.email });

    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found',
        message: 'User profile not found'
      });
      return;
    }

    const userResponse: IUserResponse = {
      id: (user._id as any).toString(),
      name: user.name,
      email: user.email,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt.toISOString(),
      lastLogin: user.lastLogin?.toISOString(),
      totalQuizzes: user.quizzes.length,
      totalTests: user.tests.length
    };

    res.json({
      success: true,
      message: 'Profile retrieved successfully',
      data: userResponse
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get profile',
      message: 'An error occurred while retrieving the profile'
    });
  }
});

/**
 * PUT /api/users/profile
 * Update user profile (protected route)
 */
router.put('/profile', authenticateToken, async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated',
        message: 'Authentication required'
      });
      return;
    }

    const { password } = req.body;
    const user = await User.findOne({ email: req.user.email });

    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found',
        message: 'User profile not found'
      });
      return;
    }

    // Update password if provided
    if (password) {
      user.password = await bcrypt.hash(password, 12);
      await user.save();
    }

    const userResponse: IUserResponse = {
      id: (user._id as any).toString(),
      name: user.name,
      email: user.email,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt.toISOString(),
      lastLogin: user.lastLogin?.toISOString(),
      totalQuizzes: user.quizzes.length,
      totalTests: user.tests.length
    };

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: userResponse
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update profile',
      message: 'An error occurred while updating the profile'
    });
  }
});

/**
 * DELETE /api/users/profile
 * Delete user account (protected route)
 */
router.delete('/profile', authenticateToken, async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated',
        message: 'Authentication required'
      });
      return;
    }

    const user = await User.findOneAndDelete({ email: req.user.email });

    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found',
        message: 'User profile not found'
      });
      return;
    }

    res.json({
      success: true,
      message: 'Account deleted successfully'
    });

  } catch (error) {
    console.error('Delete profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete profile',
      message: 'An error occurred while deleting the profile'
    });
  }
});

export default router;
