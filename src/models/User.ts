import { Schema, model } from 'mongoose';
import { IUser } from '../types/index.js';

const userSchema = new Schema<IUser>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    index: true
  },
  password: {
    type: String,
    required: true
  },
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: {
    type: String,
    required: false
  },
  emailVerificationExpires: {
    type: Date,
    required: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  lastLogin: {
    type: Date,
    required: false
  },
  quizzes: [{
    type: String,
    ref: 'Quiz'
  }],
  tests: [{
    type: String,
    ref: 'Test'
  }]
}, {
  timestamps: true,
  versionKey: false
});

// Index for better query performance
userSchema.index({ email: 1 });
userSchema.index({ createdAt: -1 });
userSchema.index({ emailVerificationToken: 1 });

// Virtual for user statistics
userSchema.virtual('totalQuizzes').get(function() {
  return this.quizzes.length;
});

userSchema.virtual('totalTests').get(function() {
  return this.tests.length;
});

// Ensure virtual fields are serialized
userSchema.set('toJSON', {
  virtuals: true,
  transform: function(_doc, ret) {
    const { _id, __v, password, emailVerificationToken, emailVerificationExpires, ...cleanRet } = ret;
    return { id: _id, ...cleanRet };
  }
});

export const User = model<IUser>('User', userSchema);
