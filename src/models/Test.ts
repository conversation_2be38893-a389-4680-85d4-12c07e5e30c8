import { Schema, model } from 'mongoose';
import { ITest, ITestAnswer, ITestFeedback } from '../types/index.js';

const testAnswerSchema = new Schema<ITestAnswer>({
  questionId: {
    type: String,
    required: true
  },
  answer: {
    type: Schema.Types.Mixed,
    required: true
  }
}, {
  _id: false,
  versionKey: false
});

const testFeedbackSchema = new Schema<ITestFeedback>({
  grade: {
    type: String,
    required: true,
    enum: ['A', 'B', 'C', 'D', 'F']
  },
  message: {
    type: String,
    required: true,
    trim: true
  },
  suggestions: [{
    type: String,
    trim: true
  }]
}, {
  _id: false,
  versionKey: false
});

const testSchema = new Schema<ITest>({
  userId: {
    type: String,
    required: true,
    ref: 'User',
    index: true
  },
  quizId: {
    type: String,
    required: true,
    ref: 'Quiz',
    index: true
  },
  answers: [testAnswerSchema],
  score: {
    type: Number,
    required: true,
    min: 0
  },
  totalQuestions: {
    type: Number,
    required: true,
    min: 1
  },
  correctAnswers: {
    type: Number,
    required: true,
    min: 0
  },
  percentage: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  timeSpent: {
    type: Number,
    required: false,
    min: 0
  },
  startTime: {
    type: Date,
    required: false
  },
  endTime: {
    type: Date,
    required: true,
    default: Date.now
  },
  submittedAt: {
    type: Date,
    required: true,
    default: Date.now
  },
  feedback: testFeedbackSchema
}, {
  timestamps: true,
  versionKey: false
});

// Indexes for better query performance
testSchema.index({ userId: 1, submittedAt: -1 });
testSchema.index({ quizId: 1 });
testSchema.index({ percentage: -1 });
testSchema.index({ submittedAt: -1 });

// Virtual for test summary
testSchema.virtual('summary').get(function() {
  return {
    id: this._id,
    quizId: this.quizId,
    score: this.score,
    totalQuestions: this.totalQuestions,
    percentage: this.percentage,
    timeSpent: this.timeSpent,
    submittedAt: this.submittedAt
  };
});

// Ensure virtual fields are serialized
testSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    const { _id, __v, ...cleanRet } = ret;
    return { id: _id, ...cleanRet };
  }
});

export const Test = model<ITest>('Test', testSchema);
