import { Schema, model } from 'mongoose';
import { IQuiz, IQuestion } from '../types/index.js';

const questionSchema = new Schema<IQuestion>({
  id: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['multiple-choice', 'true-false', 'short-answer'],
    required: true
  },
  question: {
    type: String,
    required: true,
    trim: true
  },
  options: [{
    type: String,
    trim: true
  }],
  correctAnswer: {
    type: Schema.Types.Mixed,
    required: true
  },
  explanation: {
    type: String,
    trim: true
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    required: true
  }
}, {
  _id: false,
  versionKey: false
});

const quizSchema = new Schema<IQuiz>({
  userId: {
    type: String,
    required: true,
    ref: 'User',
    index: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  text: {
    type: String,
    required: true,
    trim: true
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    required: true,
    default: 'medium'
  },
  questionType: {
    type: String,
    enum: ['multiple-choice', 'true-false', 'short-answer', 'mixed'],
    required: true,
    default: 'multiple-choice'
  },
  questions: [questionSchema],
  createdAt: {
    type: Date,
    default: Date.now
  },
  metadata: {
    textLength: {
      type: Number,
      required: true
    },
    numberOfQuestions: {
      type: Number,
      required: true
    },
    estimatedTime: {
      type: Number,
      required: true
    }
  }
}, {
  timestamps: true,
  versionKey: false
});

// Indexes for better query performance
quizSchema.index({ userId: 1, createdAt: -1 });
quizSchema.index({ difficulty: 1 });
quizSchema.index({ questionType: 1 });
quizSchema.index({ 'metadata.numberOfQuestions': 1 });

// Virtual for quiz summary
quizSchema.virtual('summary').get(function() {
  return {
    id: this._id,
    title: this.title,
    difficulty: this.difficulty,
    questionType: this.questionType,
    numberOfQuestions: this.questions.length,
    estimatedTime: this.metadata.estimatedTime,
    createdAt: this.createdAt
  };
});

// Ensure virtual fields are serialized
quizSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    const { _id, __v, ...cleanRet } = ret;
    return { id: _id, ...cleanRet };
  }
});

export const Quiz = model<IQuiz>('Quiz', quizSchema);
