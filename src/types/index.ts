import { Document } from 'mongoose';

// User interfaces
export interface IUser extends Document {
  name: string;
  email: string;
  password: string;
  emailVerified: boolean;
  emailVerificationToken?: string;
  emailVerificationExpires?: Date;
  createdAt: Date;
  lastLogin?: Date;
  quizzes: string[];
  tests: string[];
}

export interface IUserRegistration {
  name: string;
  email: string;
  password: string;
}

export interface IUserLogin {
  email: string;
  password: string;
}

export interface IUserResponse {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  createdAt: string;
  lastLogin?: string | undefined;
  totalQuizzes?: number;
  totalTests?: number;
}

// Quiz interfaces
export interface IQuestion {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer';
  question: string;
  options?: string[] | undefined;
  correctAnswer: number | boolean | string;
  explanation?: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface IQuiz extends Document {
  userId: string;
  title: string;
  text: string;
  difficulty: 'easy' | 'medium' | 'hard';
  questionType: 'multiple-choice' | 'true-false' | 'short-answer' | 'mixed';
  questions: IQuestion[];
  createdAt: Date;
  metadata: {
    textLength: number;
    numberOfQuestions: number;
    estimatedTime: number;
  };
}

export interface IQuizGeneration {
  text: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  questionType?: 'multiple-choice' | 'true-false' | 'short-answer' | 'mixed';
  numberOfQuestions?: number;
  subject?: string | undefined;
}

export interface IQuizResponse {
  id: string;
  title: string;
  difficulty: string;
  questionType: string;
  questions: IQuestion[];
  metadata: {
    textLength: number;
    numberOfQuestions: number;
    estimatedTime: number;
  };
  createdAt: string;
}

// Test interfaces
export interface ITestAnswer {
  questionId: string;
  answer: number | boolean | string;
}

export interface ITestFeedback {
  grade: string;
  message: string;
  suggestions: string[];
}

export interface ITest extends Document {
  userId: string;
  quizId: string;
  answers: ITestAnswer[];
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  percentage: number;
  timeSpent?: number;
  startTime?: Date;
  endTime: Date;
  submittedAt: Date;
  feedback: ITestFeedback;
}

export interface ITestSubmission {
  quizId: string;
  answers: (number | boolean | string)[];
  timeSpent?: number;
  startTime?: string;
  endTime?: string;
}

export interface ITestResponse {
  id: string;
  quizId: string;
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  percentage: number;
  timeSpent?: number | undefined;
  feedback: ITestFeedback;
  submittedAt: string;
}

export interface ITestStatistics {
  averageScore: number;
  totalTests: number;
  bestScore: number;
  averageTime: number;
}

// JWT interfaces
export interface IJWTPayload {
  email: string;
  userId: string;
  iat?: number;
  exp?: number;
}

// API Response interfaces
export interface IApiResponse<T = any> {
  message: string;
  data?: T;
  error?: string;
  success: boolean;
}

export interface ILoginResponse {
  message: string;
  token: string;
  user: IUserResponse;
}

export interface IRegistrationResponse {
  message: string;
  user: {
    id: string;
    name: string;
    email: string;
    emailVerified: boolean;
  };
}

// Environment variables
export interface IEnvironmentVariables {
  PORT: string;
  NODE_ENV: string;
  JWT_SECRET: string;
  MONGODB_URI: string;
  GEMINI_API_KEY?: string;
  CORS_ORIGIN?: string;
  RATE_LIMIT_WINDOW_MS?: string;
  RATE_LIMIT_MAX_REQUESTS?: string;
  BASE_URL?: string;
  SMTP_HOST?: string;
  SMTP_PORT?: string;
  SMTP_SECURE?: string;
  SMTP_USER?: string;
  SMTP_PASS?: string;
  SMTP_FROM?: string;
}
