import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import database from './utils/database.js';
import { emailService } from './services/emailService.js';
import userRoutes from './routes/users.js';
import quizRoutes from './routes/quiz.js';
import testRoutes from './routes/tests.js';
import adminRoutes from './routes/admin.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Connect to MongoDB
async function connectToDatabase(): Promise<void> {
  try {
    await database.connect();
  } catch (error) {
    console.error('Failed to connect to database:', error);
    process.exit(1);
  }
}

// API Routes
app.use('/api/users', userRoutes);
app.use('/api/quiz', quizRoutes);
app.use('/api/quizzes', quizRoutes);
app.use('/api/tests', testRoutes);
app.use('/api/admin', adminRoutes);

// Health check endpoint
app.get('/health', (req: Request, res: Response) => {
  const dbStatus = database.getConnectionInfo();

  res.status(200).json({
    status: 'OK',
    message: 'Quizzio server is running',
    timestamp: new Date().toISOString(),
    database: {
      connected: dbStatus.isConnected,
      readyState: dbStatus.readyState,
      host: dbStatus.host,
      port: dbStatus.port,
      name: dbStatus.name
    },
    environment: process.env.NODE_ENV || 'development',
    version: '2.0.0'
  });
});

// Root endpoint
app.get('/', (req: Request, res: Response) => {
  res.json({
    message: 'Welcome to Quizzio API v2.0',
    version: '2.0.0',
    technology: 'TypeScript + Express + MongoDB',
    endpoints: {
      'POST /api/users/login': 'Authenticate a user using phone number',
      'GET /api/users/profile': 'Get user profile (protected)',
      'PUT /api/users/profile': 'Update user profile (protected)',
      'DELETE /api/users/profile': 'Delete user account (protected)',
      'POST /api/quiz/generate': 'Generate a quiz from text with difficulty and question type',
      'GET /api/quizzes': 'Fetch all quizzes saved by the authenticated user (protected)',
      'GET /api/quizzes/:id': 'Get specific quiz by ID (protected)',
      'DELETE /api/quizzes/:id': 'Delete specific quiz by ID (protected)',
      'POST /api/tests/submit': 'Save the results of a completed test (protected)',
      'GET /api/tests': 'Get all test results for user (protected)',
      'GET /api/tests/:id': 'Get specific test result by ID (protected)',
      'DELETE /api/tests/:id': 'Delete specific test result by ID (protected)'
    },
    documentation: {
      health: '/health',
      swagger: '/api-docs (coming soon)'
    }
  });
});

// Error handling middleware
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error('Error stack:', err.stack);

  res.status(500).json({
    success: false,
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use((req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    message: `The route ${req.originalUrl} does not exist`,
    availableRoutes: [
      'GET /',
      'GET /health',
      'POST /api/users/login',
      'GET /api/users/profile',
      'POST /api/quiz/generate',
      'GET /api/quizzes',
      'POST /api/tests/submit',
      'GET /api/tests'
    ]
  });
});

// Start server
async function startServer(): Promise<void> {
  try {
    // Connect to database first
    await connectToDatabase();

    // Test email service connection
    await emailService.testConnection();

    // Start the server
    app.listen(PORT, () => {
      console.log('🚀 Quizzio TypeScript server is running!');
      console.log(`📍 Server URL: http://localhost:${PORT}`);
      console.log(`🏥 Health check: http://localhost:${PORT}/health`);
      console.log(`🗄️  Database: ${database.getConnectionStatus() ? 'Connected' : 'Disconnected'}`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📝 TypeScript: Enabled`);
      console.log(`📦 ES Modules: Enabled`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...');
  await database.disconnect();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully...');
  await database.disconnect();
  process.exit(0);
});

// Start the server
startServer().catch((error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

export default app;
