import { IQuestion } from '../types/index.js';
import GeminiService from '../services/geminiService.js';

export interface QuizGenerationResult {
  questions: IQuestion[];
  metadata: {
    generatedAt: string;
    sourceTextLength: number;
    difficulty: string;
    questionType: string;
    processingTime?: number;
    aiGenerated?: boolean;
  };
}

/**
 * Generate quiz questions from text using Gemini AI
 */
export async function generateQuizFromText(
  text: string,
  difficulty: 'easy' | 'medium' | 'hard' = 'medium',
  questionType: 'multiple-choice' | 'true-false' | 'short-answer' | 'mixed' = 'multiple-choice',
  numberOfQuestions: number = 5,
  subject?: string
): Promise<QuizGenerationResult> {

  try {
    // Try to use Gemini AI first
    if (process.env.GEMINI_API_KEY) {
      console.log('🤖 Using Gemini AI for quiz generation...');
      const geminiService = new GeminiService();

      const result = await geminiService.generateQuiz({
        text,
        difficulty,
        questionType,
        numberOfQuestions,
        subject
      });

      return {
        questions: result.questions,
        metadata: {
          ...result.metadata,
          aiGenerated: true
        }
      };
    } else {
      console.log('⚠️  GEMINI_API_KEY not found, using fallback mock generation...');
      return await generateMockQuiz(text, difficulty, questionType, numberOfQuestions);
    }
  } catch (error) {
    console.error('❌ Gemini AI generation failed, falling back to mock generation:', error);
    return await generateMockQuiz(text, difficulty, questionType, numberOfQuestions);
  }
}

/**
 * Fallback mock quiz generation when Gemini AI is not available
 */
async function generateMockQuiz(
  text: string,
  difficulty: 'easy' | 'medium' | 'hard',
  questionType: 'multiple-choice' | 'true-false' | 'short-answer' | 'mixed',
  numberOfQuestions: number
): Promise<QuizGenerationResult> {

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  const questions: IQuestion[] = [];

  for (let i = 1; i <= numberOfQuestions; i++) {
    if (questionType === 'multiple-choice' || questionType === 'mixed') {
      questions.push(generateMultipleChoiceQuestion(i, text, difficulty));
    } else if (questionType === 'true-false') {
      questions.push(generateTrueFalseQuestion(i, text, difficulty));
    } else if (questionType === 'short-answer') {
      questions.push(generateShortAnswerQuestion(i, text, difficulty));
    }
  }

  return {
    questions,
    metadata: {
      generatedAt: new Date().toISOString(),
      sourceTextLength: text.length,
      difficulty,
      questionType,
      aiGenerated: false
    }
  };
}

function generateMultipleChoiceQuestion(
  index: number,
  text: string,
  difficulty: 'easy' | 'medium' | 'hard'
): IQuestion {
  const difficultyQuestions = {
    easy: [
      'What is the main topic discussed in the text?',
      'Which concept is mentioned first in the text?',
      'What is the primary focus of this content?'
    ],
    medium: [
      'Based on the text, what is the relationship between the key concepts?',
      'How does the author support their main argument?',
      'What conclusion can be drawn from the information provided?'
    ],
    hard: [
      'Analyze the underlying assumptions in the text and their implications.',
      'Critically evaluate the evidence presented and its validity.',
      'What are the potential counterarguments to the main thesis?'
    ]
  };

  const questions = difficultyQuestions[difficulty];
  const questionText = questions[Math.floor(Math.random() * questions.length)] || 'Default question text';

  return {
    id: `q${index}`,
    type: 'multiple-choice',
    question: questionText,
    options: [
      'Option A - First concept analysis',
      'Option B - Second concept analysis',
      'Option C - Third concept analysis',
      'Option D - Fourth concept analysis'
    ],
    correctAnswer: Math.floor(Math.random() * 4),
    explanation: 'This answer is based on the key points and evidence presented in the provided text.',
    difficulty
  };
}

function generateTrueFalseQuestion(
  index: number,
  text: string,
  difficulty: 'easy' | 'medium' | 'hard'
): IQuestion {
  const statements = [
    'The text presents a comprehensive overview of the main topic.',
    'The author provides sufficient evidence to support their claims.',
    'The concepts discussed are interconnected and build upon each other.',
    'The text suggests practical applications for the discussed concepts.'
  ];

  return {
    id: `q${index}`,
    type: 'true-false',
    question: statements[Math.floor(Math.random() * statements.length)] || 'Default true/false statement',
    correctAnswer: Math.random() > 0.5,
    explanation: 'This statement aligns with the main themes and evidence presented in the text.',
    difficulty
  };
}

function generateShortAnswerQuestion(
  index: number,
  text: string,
  difficulty: 'easy' | 'medium' | 'hard'
): IQuestion {
  const prompts = [
    'Summarize the main argument presented in the text.',
    'Explain how the key concepts relate to each other.',
    'Describe the evidence used to support the main claims.',
    'Analyze the implications of the ideas discussed.'
  ];

  return {
    id: `q${index}`,
    type: 'short-answer',
    question: prompts[Math.floor(Math.random() * prompts.length)] || 'Default short answer prompt',
    correctAnswer: 'A comprehensive answer should address the key points mentioned in the text and demonstrate understanding of the main concepts.',
    explanation: 'The answer should reflect the main themes and supporting evidence from the provided text.',
    difficulty
  };
}

/**
 * Calculate test score based on answers and quiz questions
 */
export function calculateTestScore(
  userAnswers: (number | boolean | string)[],
  questions: IQuestion[]
): {
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  percentage: number;
} {
  let correctAnswers = 0;
  const totalQuestions = questions.length;

  for (let i = 0; i < Math.min(userAnswers.length, questions.length); i++) {
    const userAnswer = userAnswers[i];
    const correctAnswer = questions[i]?.correctAnswer;

    if (userAnswer === correctAnswer) {
      correctAnswers++;
    }
  }

  const percentage = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;

  return {
    score: correctAnswers,
    totalQuestions,
    correctAnswers,
    percentage
  };
}

/**
 * Generate feedback based on test performance
 */
export function generateTestFeedback(percentage: number): {
  grade: string;
  message: string;
  suggestions: string[];
} {
  if (percentage >= 90) {
    return {
      grade: 'A',
      message: 'Excellent work! You have a strong understanding of the material.',
      suggestions: ['Keep up the great work!', 'Consider helping others with this topic.']
    };
  } else if (percentage >= 80) {
    return {
      grade: 'B',
      message: 'Good job! You have a solid grasp of most concepts.',
      suggestions: ['Review the questions you missed.', 'Practice similar problems.']
    };
  } else if (percentage >= 70) {
    return {
      grade: 'C',
      message: 'Fair performance. There\'s room for improvement.',
      suggestions: ['Review the material again.', 'Focus on areas where you struggled.']
    };
  } else if (percentage >= 60) {
    return {
      grade: 'D',
      message: 'You\'re getting there, but need more practice.',
      suggestions: ['Spend more time studying the material.', 'Consider seeking additional help.']
    };
  } else {
    return {
      grade: 'F',
      message: 'More study is needed to master this material.',
      suggestions: ['Review all the material thoroughly.', 'Consider retaking the quiz after studying.']
    };
  }
}
