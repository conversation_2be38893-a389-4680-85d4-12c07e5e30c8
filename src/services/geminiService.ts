import { GoogleGenerativeAI } from '@google/generative-ai';
import { IQuestion } from '../types/index.js';

interface GeminiConfig {
  apiKey: string;
  model: string;
  temperature: number;
  maxOutputTokens: number;
}

interface QuizGenerationParams {
  text: string;
  difficulty: 'easy' | 'medium' | 'hard';
  questionType: 'multiple-choice' | 'true-false' | 'short-answer' | 'mixed';
  numberOfQuestions: number;
  language?: string | undefined;
  subject?: string | undefined;
}

interface GeminiQuizResponse {
  questions: IQuestion[];
  metadata: {
    generatedAt: string;
    sourceTextLength: number;
    difficulty: string;
    questionType: string;
    processingTime: number;
  };
}

class GeminiService {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private config: GeminiConfig;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY is not defined in environment variables');
    }

    this.config = {
      apiKey,
      model: 'gemini-2.5-flash',
      temperature: 0.7,
      maxOutputTokens: 4096
    };

    this.genAI = new GoogleGenerativeAI(this.config.apiKey);
    this.model = this.genAI.getGenerativeModel({
      model: this.config.model,
      generationConfig: {
        temperature: this.config.temperature,
        maxOutputTokens: this.config.maxOutputTokens,
      }
    });
  }

  /**
   * Generate quiz questions using Gemini AI
   */
  async generateQuiz(params: QuizGenerationParams): Promise<GeminiQuizResponse> {
    const startTime = Date.now();

    try {
      const prompt = this.buildPrompt(params);
      console.log('🤖 Generating quiz with Gemini AI...');

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Parse the JSON response
      const parsedQuestions = this.parseGeminiResponse(text, params);

      const processingTime = Date.now() - startTime;
      console.log(`✅ Quiz generated successfully in ${processingTime}ms`);

      return {
        questions: parsedQuestions,
        metadata: {
          generatedAt: new Date().toISOString(),
          sourceTextLength: params.text.length,
          difficulty: params.difficulty,
          questionType: params.questionType,
          processingTime
        }
      };
    } catch (error) {
      console.error('❌ Gemini AI generation error:', error);
      throw new Error(`Failed to generate quiz: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Build modular prompt based on quiz parameters
   */
  private buildPrompt(params: QuizGenerationParams): string {
    const {
      text,
      difficulty,
      questionType,
      numberOfQuestions,
      language = 'English',
      subject = 'General'
    } = params;

    const basePrompt = this.getBasePrompt();
    const difficultyPrompt = this.getDifficultyPrompt(difficulty);
    const typePrompt = this.getQuestionTypePrompt(questionType);
    const formatPrompt = this.getFormatPrompt();
    const examplePrompt = this.getExamplePrompt(questionType);

    return `${basePrompt}

${difficultyPrompt}

${typePrompt}

CONTENT TO ANALYZE:
"""
${text}
"""

REQUIREMENTS:
- Generate exactly ${numberOfQuestions} questions
- Language: ${language}
- Subject area: ${subject}
- Difficulty level: ${difficulty}
- Question type: ${questionType}
- Ensure questions test comprehension, analysis, and application of the content
- Make questions engaging and educational
- Provide clear, accurate explanations for each answer

${formatPrompt}

${examplePrompt}

Now generate the quiz based on the provided content:`;
  }

  /**
   * Base prompt for quiz generation
   */
  private getBasePrompt(): string {
    return `You are an expert educational content creator and quiz designer. Your task is to create high-quality, engaging quiz questions based on the provided text content.

Your questions should:
- Test deep understanding, not just memorization
- Be clear, unambiguous, and well-written
- Have educational value and promote learning
- Be appropriate for the specified difficulty level
- Cover different aspects of the content comprehensively`;
  }

  /**
   * Difficulty-specific prompts
   */
  private getDifficultyPrompt(difficulty: string): string {
    const difficultyPrompts = {
      easy: `DIFFICULTY: EASY
- Focus on basic comprehension and recall
- Use straightforward language and concepts
- Test main ideas and key facts
- Questions should be accessible to beginners
- Avoid complex analysis or synthesis`,

      medium: `DIFFICULTY: MEDIUM
- Test understanding and application of concepts
- Include some analysis and interpretation
- Connect different ideas within the content
- Require moderate critical thinking
- Balance recall with comprehension`,

      hard: `DIFFICULTY: HARD
- Require deep analysis and critical thinking
- Test synthesis of multiple concepts
- Include evaluation and judgment questions
- Challenge assumptions and explore implications
- Require advanced reasoning and application`
    };

    return difficultyPrompts[difficulty as keyof typeof difficultyPrompts] || difficultyPrompts.medium;
  }

  /**
   * Question type-specific prompts
   */
  private getQuestionTypePrompt(questionType: string): string {
    const typePrompts = {
      'multiple-choice': `QUESTION TYPE: MULTIPLE CHOICE
- Create 4 plausible options (A, B, C, D)
- Make distractors believable but clearly incorrect
- Avoid "all of the above" or "none of the above"
- Ensure only one option is definitively correct
- Make options similar in length and complexity`,

      'true-false': `QUESTION TYPE: TRUE/FALSE
- Create clear, unambiguous statements
- Avoid absolute terms unless they're accurate
- Test important concepts, not trivial details
- Ensure statements are definitively true or false
- Provide clear reasoning in explanations`,

      'short-answer': `QUESTION TYPE: SHORT ANSWER
- Ask for specific, concise responses
- Focus on key concepts and understanding
- Provide clear criteria for correct answers
- Include sample acceptable answers
- Test application and analysis, not just recall`,

      'mixed': `QUESTION TYPE: MIXED
- Use a variety of question types (multiple-choice, true/false, short-answer)
- Distribute types evenly across the quiz
- Match question type to the concept being tested
- Ensure variety enhances learning objectives`
    };

    return typePrompts[questionType as keyof typeof typePrompts] || typePrompts['multiple-choice'];
  }

  /**
   * JSON format specification
   */
  private getFormatPrompt(): string {
    return `OUTPUT FORMAT:
Return your response as a valid JSON array containing question objects. Each question must have this exact structure:

{
  "id": "q1",
  "type": "multiple-choice" | "true-false" | "short-answer",
  "question": "The question text",
  "options": ["Option A", "Option B", "Option C", "Option D"], // Only for multiple-choice
  "correctAnswer": 0 | true | "sample answer text", // Index for MC, boolean for T/F, string for SA
  "explanation": "Clear explanation of why this is correct",
  "difficulty": "easy" | "medium" | "hard"
}

IMPORTANT:
- Return ONLY the JSON array, no additional text
- Ensure valid JSON syntax
- Use double quotes for all strings
- For multiple-choice: correctAnswer is the index (0-3) of the correct option
- For true-false: correctAnswer is boolean (true/false)
- For short-answer: correctAnswer is a sample correct response`;
  }

  /**
   * Example prompts for different question types
   */
  private getExamplePrompt(questionType: string): string {
    if (questionType === 'multiple-choice') {
      return `EXAMPLE MULTIPLE CHOICE:
{
  "id": "q1",
  "type": "multiple-choice",
  "question": "What is the primary function of photosynthesis in plants?",
  "options": [
    "To convert sunlight into chemical energy",
    "To absorb water from the soil",
    "To release oxygen into the atmosphere",
    "To transport nutrients throughout the plant"
  ],
  "correctAnswer": 0,
  "explanation": "Photosynthesis primarily converts light energy into chemical energy (glucose), which plants use for growth and metabolism.",
  "difficulty": "medium"
}`;
    } else if (questionType === 'true-false') {
      return `EXAMPLE TRUE/FALSE:
{
  "id": "q1",
  "type": "true-false",
  "question": "Artificial intelligence can completely replace human creativity in all fields.",
  "correctAnswer": false,
  "explanation": "While AI can assist and augment human creativity, it cannot completely replace the unique aspects of human creativity such as emotional depth, cultural context, and subjective experience.",
  "difficulty": "medium"
}`;
    } else if (questionType === 'short-answer') {
      return `EXAMPLE SHORT ANSWER:
{
  "id": "q1",
  "type": "short-answer",
  "question": "Explain how machine learning algorithms improve their performance over time.",
  "correctAnswer": "Machine learning algorithms improve through training on data, adjusting weights and parameters based on feedback, and learning patterns to make better predictions or decisions.",
  "explanation": "The answer should mention training, data learning, parameter adjustment, and performance improvement through experience.",
  "difficulty": "medium"
}`;
    }
    return '';
  }

  /**
   * Parse Gemini response and validate questions
   */
  private parseGeminiResponse(response: string, params: QuizGenerationParams): IQuestion[] {
    try {
      // Clean the response to extract JSON
      const jsonMatch = response.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No valid JSON array found in response');
      }

      const questions: IQuestion[] = JSON.parse(jsonMatch[0]);

      // Validate and clean questions
      return questions.map((q, index) => ({
        id: q.id || `q${index + 1}`,
        type: q.type || 'multiple-choice',
        question: q.question || 'Generated question',
        options: q.options || undefined,
        correctAnswer: q.correctAnswer,
        explanation: q.explanation || 'No explanation provided',
        difficulty: q.difficulty || params.difficulty
      })).slice(0, params.numberOfQuestions); // Ensure we don't exceed requested number

    } catch (error) {
      console.error('Failed to parse Gemini response:', error);
      console.log('Raw response:', response);
      throw new Error('Failed to parse AI response. Please try again.');
    }
  }

  /**
   * Test connection to Gemini API
   */
  async testConnection(): Promise<boolean> {
    try {
      const result = await this.model.generateContent('Say "Hello" in JSON format: {"message": "Hello"}');
      const response = await result.response;
      return response.text().includes('Hello');
    } catch (error) {
      console.error('Gemini connection test failed:', error);
      return false;
    }
  }
}

export default GeminiService;
