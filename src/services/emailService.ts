import nodemailer from 'nodemailer';
import { v4 as uuidv4 } from 'uuid';

interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

interface VerificationEmailData {
  name: string;
  email: string;
  verificationToken: string;
}

class EmailService {
  private transporter: nodemailer.Transporter;
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.BASE_URL || 'http://localhost:3000';

    // Configure email transporter
    const emailConfig: EmailConfig = {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || ''
      }
    };

    this.transporter = nodemailer.createTransport(emailConfig);
  }

  /**
   * Generate a verification token
   */
  generateVerificationToken(): string {
    return uuidv4();
  }

  /**
   * Send email verification email
   */
  async sendVerificationEmail(data: VerificationEmailData): Promise<boolean> {
    try {
      const verificationUrl = `${this.baseUrl}/api/users/verify-email?token=${data.verificationToken}`;

      const mailOptions = {
        from: {
          name: 'Quizzio',
          address: process.env.SMTP_FROM || process.env.SMTP_USER || '<EMAIL>'
        },
        to: data.email,
        subject: 'Verify Your Email - Quizzio',
        html: this.getVerificationEmailTemplate(data.name, verificationUrl),
        text: this.getVerificationEmailText(data.name, verificationUrl)
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`📧 Verification email sent to ${data.email}:`, result.messageId);
      return true;
    } catch (error) {
      console.error('Failed to send verification email:', error);
      return false;
    }
  }

  /**
   * HTML template for verification email
   */
  private getVerificationEmailTemplate(name: string, verificationUrl: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your Email</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #4f46e5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Welcome to Quizzio!</h1>
          </div>
          <div class="content">
            <h2>Hi ${name},</h2>
            <p>Thank you for registering with Quizzio! To complete your registration and start creating amazing quizzes, please verify your email address.</p>
            <p>Click the button below to verify your email:</p>
            <a href="${verificationUrl}" class="button">Verify Email Address</a>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #4f46e5;">${verificationUrl}</p>
            <p><strong>This link will expire in 24 hours.</strong></p>
            <p>If you didn't create an account with Quizzio, you can safely ignore this email.</p>
          </div>
          <div class="footer">
            <p>© 2024 Quizzio. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Plain text version for verification email
   */
  private getVerificationEmailText(name: string, verificationUrl: string): string {
    return `
Hi ${name},

Welcome to Quizzio!

Thank you for registering with Quizzio! To complete your registration and start creating amazing quizzes, please verify your email address.

Please click on the following link to verify your email:
${verificationUrl}

This link will expire in 24 hours.

If you didn't create an account with Quizzio, you can safely ignore this email.

© 2024 Quizzio. All rights reserved.
    `.trim();
  }

  /**
   * Test email configuration
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log('📧 Email service is ready');
      return true;
    } catch (error) {
      console.error('❌ Email service configuration error:', error);
      return false;
    }
  }
}

export const emailService = new EmailService();
