{"name": "quizzio", "version": "1.0.0", "type": "module", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "tsx src/server.ts", "dev:watch": "nodemon --exec \"tsx\" src/server.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.24.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.3", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.19.0", "morgan": "^1.10.1", "nodemailer": "^7.0.9", "uuid": "^13.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/helmet": "^0.0.48", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.6.2", "@types/nodemailer": "^7.0.2", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsx": "^4.20.6", "typescript": "^5.9.3"}}